class Producto:
    def __init__(self, nombre, precio, stock):
        self.nombre = nombre
        self.precio = precio
        self.stock = stock
        
def ver_productos(listado):
    print("=== LISTADO DE PRODUCTOS ===")
    print("Nombre | Precio | Stock")
    for prod in listado.values():
        print(f"{prod.nombre} | {prod.precio} | {prod.stock}")
        
def agregar_productos(listado):
    name = input("Ingrese el nombre del producto: ")
    price = int(input("Ingrese el precio del producto: "))
    stockk = int(input("Ingrese el stock del producto: "))
    listado[name]=Producto(name, price, stockk)

def comprar_productos(productos,carrito):
    name = input("Ingrese el nombre del producto: ")
    if name in productos:
        print(f"El producto {name} cuesta {productos[name].precio}")
        cantidad = int(input("Ingrese la cantidad del producto: "))
        if cantidad > productos[name].stock:
            print("No hay suficiente stock")
            return
        else:
            productos[name].stock -= cantidad
            carrito.append((name, cantidad))
            print("Producto agregado al carrito")
    else:
        print("El producto no existe")
        return
    
def ver_carrito(carrito):
    print("=== LISTADO DE PRODUCTOS EN EL CARRITO ===")
    print("Nombre | Cantidad")
    for nombre, cantidad in carrito:  # Desempaqueta la tupla
        print(f"{nombre} | {cantidad}")


def main():
    
    productos={"cacas":Producto("cacas", 100, 1000)}
    carrito=[]

    
    print("Bienvenido a la tienda online")
    while True:
        print("1. Ver productos")
        print("2. Comprar productos")
        print("3. Agregar productos")
        print("4. Ver carrito")
        print("5. Salir")
        opcion = int(input("Ingrese una opcion: "))
        if opcion == 1:
            ver_productos(productos)
        elif opcion == 2:
            comprar_productos(productos, carrito)
        elif opcion == 3:
            agregar_productos(productos)   
        elif opcion == 4:
            ver_carrito(carrito)
        elif opcion == 5:
            print("Gracias por su compra")
            break
        else:
            print("Opcion invalida")
          
    
    
main()
    
