class User:
    def __init__(self,user,passw):
        self.user = user
        self.passw = passw
        
class Admin(User):
    def __init__(self,usuario,contrasena):
        super().__init__(usuario,contrasena)
        self.admin = True

class Usuario(User):
    def __init__(self,usuario,contrasena):
        super().__init__(usuario,contrasena)
        self.admin = False
        
def login(lista,usuario,contrasena):
    if usuario not in lista:
        print("Usuario no encontrado")
    elif contrasena != lista[usuario].passw:
        print("Contraseña incorrecta")
    else:
        if lista[usuario].admin == True:
            print("Bienvenido administrador")
        else:
            print("Bienvenido")
        return True
    
def crearPerfilAdmin(diccionario,usuario,contrasena):
    if usuario in diccionario:
        print("Usuario ya existe")
    else:
        print("Creando perfil de admin")
        diccionario[usuario]=Admin(usuario,contrasena)

def crearPerfilUsuario(diccionario,usuario,contrasena):
    if usuario in diccionario:
        print("Usuario ya existe")
    else:
        print("Creando perfil de usuario")
        diccionario[usuario]=Usuario(usuario,contrasena)
    
def listadoDeUsuarios(diccionario):
    for usuario,objeto in diccionario.items():
        print(usuario,objeto.passw,objeto.admin)    # llamo al atributo passw del objeto
        



def main():
    diccionarioUsuarios = {"juan":Admin("juan","1234"),"pedro":Usuario("pedro","5678"),"maria":Usuario("maria","91011")}
    
    listadoDeUsuarios(diccionarioUsuarios)
    
    while True:
        try:
            opcion = int(input("Bienvenido al sistema de login elija una opcion 1. Loguin 2. Crear perfil 3. Ver Usuarios 4. Imprimir 5. Salir "))     
            if opcion == 1:
                usuario = input("Ingrese su usuario: ").lower()
                contrasena = input("Ingrese su contraseña: ").lower()
                login(diccionarioUsuarios,usuario,contrasena)
            elif opcion == 2:
                usuario = input("Ingrese su usuario: ").lower()
                contrasena = input("Ingrese su contraseña: ").lower()
                admin = input("Es admin? (s/n): ").lower()
                if admin == "s":
                    crearPerfilAdmin(diccionarioUsuarios,usuario,contrasena)
                else:
                    crearPerfilUsuario(diccionarioUsuarios,usuario,contrasena)          
            elif opcion == 3:
                listadoDeUsuarios(diccionarioUsuarios)
            elif opcion == 4:
                print("Imprimiendo...")
                
                
            elif opcion == 5:
                print("Saliendo...")
                break
            else:
                print("Opcion no valida")
        except ValueError:
            print("Error: Ingrese un numero valido")

        
main()    