class Comentario:
    def __init__(self, usuario, mensaje):
        self.usuario = usuario
        self.mensaje = mensaje

    def mostrar(self):
        print(f"{self.usuario} dice: {self.mensaje}")

def agregar_comentario(comentarios, usuarios_registrados):
    usuario = input("Ingrese nombre de usuario: ")
    if usuario not in usuarios_registrados:
        print("Usuario no registrado")
        return
    mensaje = input("Ingrese su comentario: ")
    comentario = Comentario(usuario, mensaje)
    comentarios.append(comentario)
    print("Comentario agregado.")

def mostrar_comentarios(comentarios):
    if comentarios:
        print("\nLista de comentarios:")
        for c in comentarios:
            c.mostrar()
    else:
        print("No hay comentarios aún.")

def main_practica():
    usuarios_registrados = ["juan", "maria", "luis"]
    comentarios = []
    while True:
        print("\n1. Agregar comentario")
        print("2. Mostrar comentarios")
        print("3. Salir")
        opcion = input("Seleccione una opción: ")
        if opcion == "1":
            agregar_comentario(comentarios, usuarios_registrados)
        elif opcion == "2":
            mostrar_comentarios(comentarios)
        elif opcion == "3":
            print("Finalizando sesión.")
            break
        else:
            print("Opción inválida.")


main_practica()