class Producto:
    def __init__(self, codigo,nombre,precio,stock ):
        self.codigo = codigo
        self.nombre = nombre
        self.precio = precio
        self.stock = stock
        
def agregar_productos(listado,codProd):
    print("AGREGAR PRODUCTOS")
    print()
    
    
    
def actualizar_stock(listado):
    print("ACTUALIZAR STOCK")
    print()
    
    
def mostrar_productos(listado):
    print("LISTADO DE PRODUCTOS")
    print()
    if listado == {}:
        print("Aun no hay productos registrados por favor seleccione la opcion 1 para agregar un producto")
    else:
        print("Codigo | Nombre | Precio | Stock")
        for prod in listado.values():
            print(f"{prod.codigo} | {prod.nombre} | {prod.precio} | {prod.stock}")
    
def main():
    codProd=0
    listado_productos = {}
    while True:
        print("Bienvenido al sistema de gestion de productos")
        print()
        print("1. Agregar producto")
        print("2. Actualizar stock")
        print("3. Mostrar productos")
        print("4. Salir")
        print()
        try:
            opcion = int(input("Ingrese una opcion: \n"))     
            if opcion == 1:
                agregar_productos(listado_productos,codProd)
            elif opcion == 2:
                actualizar_stock(listado_productos)
            elif opcion == 3:
                mostrar_productos(listado_productos)
            elif opcion == 4:
                print("Gracias por usar el sistema")
                break
            else:
                print("Opcion invalida ingrese una de las opciones disponibles")
        except ValueError:
            print("Opcion invalida ingrese un numero")

main()