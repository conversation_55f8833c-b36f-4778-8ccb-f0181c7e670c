class Producto:
    def __init__(self, codigo,nombre,precio,stock ):
        self.codigo = codigo
        self.nombre = nombre
        self.precio = precio
        self.stock = stock
        
def agregar_productos(listado,codProd):
    print("AGREGAR PRODUCTOS")
    print()
    try:
        codProd +=1
        nombre = input("Ingrese el nombre del producto: ")
        precio = int(input("Ingrese el precio del producto: "))
        stock = int(input("Ingrese el stock del producto: "))
        listado[codProd]=Producto(codProd,nombre,precio,stock)
        return codProd
    except:
        print("Error: Ingrese valores correctos, vuelva a ingresar por favor")
        print("XXX")
        print()
        return codProd - 1
        
    
def actualizar_stock(listado):
    print()
    print("ACTUALIZAR STOCK")
    print()
    if listado == {}:
        print("Aun no hay productos registrados por favor seleccione la opcion 1 para agregar un producto")
    else:
        try:
            cod = int(input("Ingrese el codigo del producto: "))
            if cod in listado:
                print(f"El stock actual del producto {listado[cod].nombre} es {listado[cod].stock}")
                
                
                
                stock = int(input("Ingrese el stock del producto: "))
                listado[cod].stock = stock
            else:
                print("El codigo ingresado no corresponde a un producto")
        except:
            print("Error: Ingrese valores correctos, vuelva a ingresar por favor")
            print("XXX")
            print()
    
    
def mostrar_productos(listado):
    print()
    print("LISTADO DE PRODUCTOS")
    print()
    if listado == {}:
        print("Aun no hay productos registrados por favor seleccione la opcion 1 para agregar un producto")
    else:
        print("Codigo | Nombre | Precio | Stock")
        for prod in listado.values():
            print(f"{prod.codigo} | {prod.nombre} | {prod.precio} | {prod.stock}")
    
def main():
    codProd=0
    listado_productos = {}
    while True:
        print("==================================================")
        print()
        print("Bienvenido al sistema de gestion de productos")
        print()
        print("1. Agregar producto")
        print("2. Actualizar stock")
        print("3. Mostrar productos")
        print("4. Salir")
        print()
        try:
            opcion = int(input("Ingrese una opcion: \n"))     
            if opcion == 1:
                codProd = agregar_productos(listado_productos,codProd)
            elif opcion == 2:
                actualizar_stock(listado_productos)
            elif opcion == 3:
                mostrar_productos(listado_productos)
            elif opcion == 4:
                print("Gracias por usar el sistema")
                break
            else:
                print("Opcion invalida ingrese una de las opciones disponibles")
        except ValueError:
            print("Opcion invalida ingrese un numero")

main()