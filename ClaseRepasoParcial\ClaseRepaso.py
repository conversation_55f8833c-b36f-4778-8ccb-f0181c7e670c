class Comentario:
    def __init__(self, usuario, mensaje):
        self.usuario = usuario
        self.mensaje = mensaje
        
def mostrarComentarios(lista):  
    print("Lista de Comentarios")
    print() 
    for comentario in lista:
        print(f"Usuario: {comentario.usuario}, Mensaje: {comentario.mensaje}")   
    print()  
        
def ingresarComentario(lista,usuario):
    comentario = input("Ingrese comentario: \n")
    lista.append(Comentario(usuario,comentario))
    
def agregarUsuario(lista,nombre):
    lista.append(nombre)
    print(f"Usuario {nombre} agregado con exito")
    
    
def main():
    lista_comentarios = []
    lista_usuarios = ["juan","pedro","luis","maria","jose","carlos"]
    eleccion = 0
    
    
    print("Bienvenido al sistema de Comentarios")
    while True:
        try:
            print()
            print("PorFavor elija una opcion")
            print()
            eleccion = int(input("1/ Mostrar Comentarios     2/ Ingresar Comentario     3/ Agregar Usuario     4/ Salir  =====> "))
            match eleccion:
                case 1:
                    print()
                    if lista_comentarios == []:
                        print("Por ahora no existen comentarios, ingrese uno")
                    else:
                        mostrarComentarios(lista_comentarios)           
                case 2:
                    print()
                    usuario =  input("Ingrese nombre de usuario: ").lower()    
                    if usuario in lista_usuarios:
                        ingresarComentario(lista_comentarios,usuario)
                    else:
                        print("Usuario no encontrado")
                        continue
                case 3:
                    print()
                    usuarioNuevo = input("Ingrese el nombre del nuevo usuario: ")
                    if usuarioNuevo in lista_usuarios:
                        print("Ya hay un usuario con ese nombre")
                    else:
                        if len(usuarioNuevo) > 10:
                            print("El nombre del usuario no puede tener mas de 10 caracteres")
                        else:
                            agregarUsuario(lista_usuarios,usuarioNuevo)    
                case 4:
                    print()
                    print("Gracias por usar el sistema")
                    break
                case _:
                    print("Opcion no valida, ingrese una de las opciones disponibles")  
        except ValueError:
            print("Escriba un numero por favor")
            


if __name__ == "__main__":
    main()