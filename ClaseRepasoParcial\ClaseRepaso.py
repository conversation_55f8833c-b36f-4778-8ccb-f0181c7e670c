class Comentario:
    def __init__(self, usuario, mensaje):
        self.usuario = usuario
        self.mensaje = mensaje
        
    
def mostrarComentarios(lista):  
    print  
    for comentario in lista:
        print(f"Usuario: {comentario.usuario}, Mensaje: {comentario.mensaje}")     
        
def ingresarComentario(lista,usuario):
    comentario = input("Ingrese comentario: ")
    lista.append(Comentario(usuario,comentario))



def main():
    lista_comentarios = []
    lista_usuarios = ["juan","pedro","luis","maria","jose","carlos"]
    eleccion = 0
    
    print("Bienvenido al sistema de Comentarios")
    while True:
        try:
            print("PorFavor elija una opcion")
            eleccion = int(input("1/ Mostrar Comentarios     2/ Ingresar Comentario     3/ Salir"))
            match eleccion:
                case 1:
                    if lista_comentarios == []:
                        print("Por ahora no existen comentarios, ingrese uno")
                    else:
                        mostrarComentarios(lista_comentarios)
                    
                case 2:
                    usuario =  input("Ingrese nombre de usuario: ")
                    if usuario in lista_usuarios:
                        ingresarComentario(lista_comentarios,usuario)
                    else:
                        print("Usuario no encontrado")
                        continue
                case 3:
                    print("Gracias por usar el sistema")
                    break
                case _:
                    print("Opcion no valida")
                    continue   
        except Exception as e:
            print(type(e).__name__)
        
            
        
    
    